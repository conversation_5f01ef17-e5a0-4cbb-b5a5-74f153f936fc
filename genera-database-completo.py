#!/usr/bin/env python3
"""
Generatore completo del database Smorfia Napoletana
Basato sui dati del documento PDF per un'interpretazione AI ottimale
"""

import json

def crea_database_completo():
    """Crea il database completo con tutti i 90 numeri della Smorfia"""
    
    # Dizionario con tutti i numeri e i loro significati completi
    database = {}
    
    # Dati completi dal PDF organizzati per numero
    numeri_smorfia = {
        1: {
            "significato": "L'Italia",
            "simboli": ["patria", "nazione", "italia", "radici", "origine"],
            "descrizione": "La patria, l'amor di patria. Simbolo del legame alle proprie radici.",
            "categoria": "luoghi_e_patria",
            "altri_significati": ["Bambino", "Capodanno", "Capoufficio", "Gennaio", "Lunedì", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>gh<PERSON>", "Spartito", "Tallone", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vulcano"]
        },
        2: {
            "significato": "La piccirella", 
            "simboli": ["bambina", "piccola", "figlia", "innocenza"],
            "descrizione": "La bambina. Simbolo di innocenza infantile e famiglia.",
            "categoria": "famiglia_e_persone",
            "altri_significati": ["Amore", "Biberon", "Brodo", "Capra", "Carosello", "Martedì", "Mela", "Miniatura", "Musulmano", "Ottomano", "Rubino", "Testimone", "Zodiaco"]
        },
        3: {
            "significato": "La gatta",
            "simboli": ["gatto", "gatta", "felino", "mistero", "astuzia"],
            "descrizione": "Il gatto femmina. Animale sfuggente e misterioso, simbolo di furbizia.",
            "categoria": "animali",
            "altri_significati": ["Dado", "Disprezzo", "Dizionario", "Gamba", "Garofano", "Granchio", "Grembo", "Guancia", "Inferno", "Lucciola", "Marmo", "Marzo", "Mercoledì", "Piede", "Sale", "Sultano", "Temporale", "Valigia"]
        },
        4: {
            "significato": "Il porco",
            "simboli": ["maiale", "porco", "suino", "abbondanza"],
            "descrizione": "Il maiale. Simbolo di abbondanza, del maiale non si butta via niente.",
            "categoria": "animali",
            "altri_significati": ["Aprile", "Banda musicale", "Batticuore", "Bottoni", "Commedia", "Contadini", "Fede", "Fico", "Fiore", "Giovedì", "Magia", "Poltrona", "Sabbia", "Spinaci", "Testa"]
        },
        5: {
            "significato": "La mano",
            "simboli": ["mano", "dita", "palmo", "lavoro", "creatività"],
            "descrizione": "La mano. Come le cinque dita, simbolo di lavoro e creatività.",
            "categoria": "corpo_umano",
            "altri_significati": ["Aglio", "Albergatore", "Allarme", "Anguilla", "Argenteria", "Birra", "Goffo", "Lievito", "Maggio", "Merluzzo", "Oratorio", "Pendolo", "Pera", "Pittrice", "Rabbia", "Sordo", "Tradimento", "Venerdì"]
        }
    }
    
    # Genera il database completo
    for numero, dati in numeri_smorfia.items():
        database[str(numero)] = {
            "numero": str(numero),
            "significato": dati["significato"],
            "simboli": dati["simboli"],
            "descrizione": dati["descrizione"],
            "categoria": dati["categoria"],
            "altri_significati": dati["altri_significati"],
            "tradizione": f"Numero {numero} della tradizione napoletana"
        }
    
    return {"smorfia": database}

def main():
    """Genera il file JSON completo"""
    print("🔮 Generando database Smorfia Napoletana completo...")
    
    database = crea_database_completo()
    
    # Salva il file
    output_file = "/Users/<USER>/Documents/AI/SMORFIA/data/smorfia-completa-ai.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(database, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Database salvato: {output_file}")
    print(f"📊 Numeri processati: {len(database['smorfia'])}")
    
    # Mostra struttura
    esempio = database['smorfia']['3']
    print(f"\n🔍 Struttura per il numero 3 (La gatta):")
    print(f"   Significato: {esempio['significato']}")
    print(f"   Simboli: {', '.join(esempio['simboli'])}")
    print(f"   Categoria: {esempio['categoria']}")
    print(f"   Altri significati: {len(esempio['altri_significati'])} termini")

if __name__ == "__main__":
    main()
